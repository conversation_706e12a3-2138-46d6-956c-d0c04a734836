<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="<PERSON><PERSON><PERSON> is a design & build studio creating stunning spaces from concept to completion. Interior design, renovation, landscaping, and turnkey projects." />
    <meta name="robots" content="index, follow" />
    <meta name="theme-color" content="#D4941E" />
    <!-- Canonical: update href to your production URL -->
    <!-- <link rel="canonical" href="https://www.your-domain.com/" /> -->

    <!-- Preconnects for faster font loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://use.typekit.net" crossorigin>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://use.typekit.net/oph7lmk.css"> <!-- Acumin Variable Concept via Adobe Fonts -->

    <!-- Styles -->
    <link rel="stylesheet" href="style.css">

    <!-- Tailwind (CDN for dev/preview; consider a production build to purge unused styles) -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Favicon -->
    <link rel="icon" href="logo.png" type="image/png">

    <!-- Open Graph / Twitter Cards -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Nirmanah Design & Build" />
    <meta property="og:description" content="Transform your space with Nirmanah — design & build excellence for homes and businesses." />
    <meta property="og:image" content="iqbal-putra-gBTb_dnfJGw-unsplash.jpg" />
    <meta property="og:image:alt" content="Modern architectural design showcasing Nirmanah's expertise" />
    <meta property="og:site_name" content="Nirmanah" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Nirmanah Design & Build" />
    <meta name="twitter:description" content="From concept to completion, we create stunning spaces that reflect your vision." />
    <meta name="twitter:image" content="iqbal-putra-gBTb_dnfJGw-unsplash.jpg" />
    <title>Nirmanah Design & Build</title>
</head>
<body>
    <section id="nav">
        <a class="skip-link" href="#main-content">Skip to main content</a>
        <!-- Navbar -->
        <header>
            <nav class="flex items-center justify-between px-8 py-4 bg-white  sticky top-0 z-50" role="navigation" aria-label="Primary">
                <!-- Logo and Tagline -->
                <div class="flex items-center space-x-2">
                    <img src="/home/<USER>/Downloads/Nirmanah/images/logo.png" alt="Nirmanah logo" class="w-10 h-10 object-contain" width="40" height="40">
                    <div class="flex flex-col">
                        <span class="text-xl text-gray-900 nirmanah-font">nirmanah</span>
                        <span class="tagline">design & build</span>
                    </div>
                </div>

                <!-- Nav Links Container -->
                <ul class="hidden md:flex items-center space-x-1 nav-links bg-gray-900 rounded-full p-2 shadow-md">
                    <li>
                        <a href="#" aria-current="page" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm text-gray-300">Home</a>
                    </li>
                    <li>
                        <a href="#about" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm text-gray-300">About</a>
                    </li>
                    <li class="dropdown">
                        <button type="button" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm flex items-center text-gray-300"
                        aria-haspopup="true" aria-expanded="false" aria-controls="services-menu">
                        <span>Services</span>
                        <svg class="dropdown-arrow w-3 h-3 fill-current" viewBox="0 0 12 12" aria-hidden="true">
                            <path d="M6 8l4-4H2z"/>
                        </svg>
                        </button>
                        <div id="services-menu" class="dropdown-menu" role="menu">
                            <a href="#" class="dropdown-item" role="menuitem">Design & Consultancy</a>
                            <a href="#" class="dropdown-item" role="menuitem">Renovation & Remodeling</a>
                            <a href="#" class="dropdown-item" role="menuitem">Landscaping & Outdoor Work</a>
                            <a href="#" class="dropdown-item" role="menuitem">Turnkey Projects</a>
                        </div>
                    </li>
                    <li>
                        <a href="#portfolio" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm text-gray-300">Portfolio</a>
                    </li>
                    <li>
                        <a href="#contact" class="px-4 py-2 rounded-full nav-item-hover transition-all duration-200 text-sm text-gray-300">Contact</a>
                    </li>
                </ul>

                <!-- Mobile Menu Button -->
                <div class="md:hidden">
                    <button onclick="toggleMobileMenu()" class="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200" aria-label="Toggle mobile menu">
                        <svg id="menu-icon" class="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                        <svg id="close-icon" class="w-6 h-6 text-gray-700 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- Get a Quote Button -->
                <div class="hidden md:block">
                    <button class="btn-primary">
                        Get a Quote
                    </button>
                </div>
            </nav>
        </header>
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="md:hidden bg-white border-b shadow-lg hidden">
            <div class="px-8 py-4 space-y-4">
                <a href="#" class="block py-2 text-gray-900 font-medium border-l-4 border-orange-500 pl-4 bg-gray-50 mobile-menu-link active">
                    Home
                </a>
                <a href="#" class="block py-2 text-gray-600 hover:text-orange-500 transition-colors duration-200 mobile-menu-link">About</a>

                <!-- Mobile Services Dropdown -->
                <div class="mobile-dropdown">
                    <button onclick="toggleMobileDropdown()" class="flex items-center justify-between w-full py-2 text-gray-600 hover:text-orange-500 transition-colors duration-200 mobile-menu-link" aria-expanded="false">
                    Services
                    <svg id="mobile-dropdown-arrow" class="w-4 h-4 transition-transform duration-300" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                    </button>
                    <div id="mobile-dropdown-content" class="mobile-dropdown-content">
                        <a href="#" class="block mobile-dropdown-item px-2 py-2">Design & Consultancy</a>
                        <a href="#" class="block mobile-dropdown-item px-2 py-2">Renovation & Remodeling</a>
                        <a href="#" class="block mobile-dropdown-item px-2 py-2">Landscaping & Outdoor Work</a>
                        <a href="#" class="block mobile-dropdown-item px-2 py-2">Turnkey Projects</a>
                    </div>
                </div>

                <a href="#" class="block py-2 text-gray-600 hover:text-orange-500 transition-colors duration-200 mobile-menu-link">Portfolio</a>
                <a href="#" class="block py-2 text-gray-600 hover:text-orange-500 transition-colors duration-200 mobile-menu-link">Contact</a>
                <hr class="my-4">
                <div class="space-y-3">
                    <button class="block w-full py-3 btn-primary rounded-full font-medium">
                    Get a Quote
                    </button>
                </div>
            </div>
        </div>
    </section>
    <main id="main-content">
        <!-- Hero Section -->
        <section class="relative py-12 overflow-hidden bg-white sm:py-16">
            <div class="relative px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl">
                <div class="max-w-4xl mx-auto text-center">
                    <p class="text-sm font-normal tracking-widest uppercase">
                <span class="text-transparent bg-clip-text text-gradient-brand"> Design & Build Excellence </span>
                    </p>
                <h1 class="mt-8 text-4xl font-normal sm:text-5xl lg:text-6xl xl:text-7xl c-primary">Transform Your Space with Nirmanah</h1>
                <p class="mt-6 text-lg sm:text-xl c-secondary">From concept to completion, we create stunning spaces that reflect your vision and enhance your lifestyle.</p>

                    <div class="flex flex-col items-center justify-center px-8 mt-12 space-y-5 sm:space-y-0 sm:px-0 sm:space-x-5 sm:flex-row">
                        <div class="relative inline-flex items-center justify-center w-full sm:w-auto group">
                    <div class="absolute transition-all duration-200 rounded-full -inset-px gradient-primary shadow-brand"></div>
                    <a href="#contact" title="" class="relative inline-flex items-center justify-center w-full px-8 py-3 text-base font-normal border border-transparent rounded-full sm:w-auto c-inverse gradient-primary" role="button"> Get Your Quote </a>
                        </div>

                <a href="#portfolio" title="" class="inline-flex items-center justify-center w-full px-8 py-3 text-base font-normal transition-all duration-200 rounded-full sm:w-auto btn-outline-brand" role="button"> View Portfolio </a>
                    </div>
                </div>
            </div>
        </section>
        <section id="about-us">
            <!-- About Us Header -->
            <div class="relative py-12 overflow-hidden bg-white sm:py-16">
                <div class="relative px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl">
                    <div class="max-w-4xl mx-auto text-center">
                        <p class="text-sm font-normal tracking-widest uppercase">
                            <span class="text-transparent bg-clip-text text-gradient-brand">About Nirmanah</span>
                        </p>
                        <h1 class="mt-8 text-4xl font-normal sm:text-5xl lg:text-6xl c-primary">
                            <span class="text-transparent bg-clip-text text-gradient-brand">Nirmanah:</span> Crafting Spaces That Inspire
                        </h1>
                        <div class="mt-6 max-w-4xl mx-auto">
                            <p class="text-lg sm:text-xl c-secondary">
                                Founded in 2018, Nirmanah is where architectural vision meets expert craftsmanship. We are a passionate team of designers, architects, and builders dedicated to creating exceptional spaces that reflect your unique style and enhance your everyday life.
                            </p>
                        </div>

                        <!-- Video Container -->
                        <div class="max-w-4xl mx-auto mt-16">
                        <div class="relative aspect-video rounded-2xl overflow-hidden shadow-2xl group">
                            <!-- Background Image -->
                            <img src="images/logo.png" alt="Nirmanah Design & Build Showcase"
                                 class="absolute inset-0 w-full h-full object-cover object-center" />

                            <!-- Overlay -->
                            <div class="absolute inset-0 flex items-center justify-center" style="background: linear-gradient(135deg, rgba(212, 148, 30, 0.9), rgba(139, 90, 43, 0.9));">
                                <!-- Play Button -->
                                <button onclick="playVideo()"
                                        class="flex items-center justify-center w-20 h-20 md:w-24 md:h-24 bg-white rounded-full shadow-lg hover:scale-110 transition-all duration-300 group-hover:shadow-2xl"
                                        aria-label="Play video">
                                    <svg class="w-8 h-8 md:w-10 md:h-10 ml-1" style="color: #D4941E;" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M8 5v14l11-7z"/>
                                    </svg>
                                    <!-- Pulse animation -->
                                    <span class="absolute inset-0 rounded-full bg-white opacity-20 animate-ping"></span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Video Modal -->
                <div id="videoModal" class="fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-75">
                    <div class="relative w-full max-w-4xl mx-4">
                        <!-- Close Button -->
                        <button onclick="closeVideo()"
                                class="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors duration-200"
                                aria-label="Close video">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>

                        <!-- Video Container -->
                        <div class="relative aspect-video bg-black rounded-lg overflow-hidden">
                            <iframe id="videoFrame"
                                    class="w-full h-full"
                                    src=""
                                    frameborder="0"
                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                    allowfullscreen>
                            </iframe>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section id="services">
            <!-- Services Section -->
            <div class="py-16" style="background: linear-gradient(135deg, #FEF7E8 0%, #F9F4E8 100%);">
                <div class="container m-auto px-6 text-gray-600 md:px-12 xl:px-0">
                    <!-- Section Header -->
                    <div class="max-w-4xl mx-auto text-center mb-16">
                        <p class="text-sm font-normal tracking-widest uppercase mb-4">
                            <span class="text-transparent bg-clip-text text-gradient-brand">Our Services</span>
                        </p>
                        <h2 class="text-3xl font-normal sm:text-4xl lg:text-5xl c-primary mb-6">
                            Comprehensive Design & Build Solutions
                        </h2>
                        <p class="text-lg c-secondary">
                            From initial concept to final completion, we offer end-to-end services to transform your vision into reality.
                        </p>
                    </div>

                    <!-- Services Grid -->
                    <div class="mx-auto grid gap-8 md:w-3/4 lg:w-full lg:grid-cols-2 xl:grid-cols-4">
                        <!-- Design & Consultancy -->
                        <div class="bg-white rounded-2xl shadow-xl px-8 py-12 sm:px-12 lg:px-8 hover:shadow-2xl transition-all duration-300 group">
                            <div class="mb-8 space-y-4">
                                <div class="w-16 h-16 rounded-full gradient-primary flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-2xl font-semibold" style="color: #8B5A2B;">Design & Consultancy</h3>
                                <p class="mb-6 text-gray-600 leading-relaxed">Architectural, structural, and interior design solutions that balance aesthetics and functionality.</p>
                                <a href="#portfolio" class="inline-flex items-center font-medium group-hover:translate-x-1 transition-transform duration-200" style="color: #D4941E;">
                                    View projects
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>

                        <!-- Renovation & Remodeling -->
                        <div class="bg-white rounded-2xl shadow-xl px-8 py-12 sm:px-12 lg:px-8 hover:shadow-2xl transition-all duration-300 group">
                            <div class="mb-8 space-y-4">
                                <div class="w-16 h-16 rounded-full gradient-primary flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-2xl font-semibold" style="color: #8B5A2B;">Renovation & Remodeling</h3>
                                <p class="mb-6 text-gray-600 leading-relaxed">Home, office, and retail renovations that enhance style, comfort, and usability.</p>
                                <a href="#portfolio" class="inline-flex items-center font-medium group-hover:translate-x-1 transition-transform duration-200" style="color: #D4941E;">
                                    View projects
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>

                        <!-- Landscaping & Outdoor Work -->
                        <div class="bg-white rounded-2xl shadow-xl px-8 py-12 sm:px-12 lg:px-8 hover:shadow-2xl transition-all duration-300 group">
                            <div class="mb-8 space-y-4">
                                <div class="w-16 h-16 rounded-full gradient-primary flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-2xl font-semibold" style="color: #8B5A2B;">Landscaping & Outdoor Works</h3>
                                <p class="mb-6 text-gray-600 leading-relaxed">Custom outdoor spaces with pergolas, sit-outs, paving, and more, blending beauty with function.</p>
                                <a href="#portfolio" class="inline-flex items-center font-medium group-hover:translate-x-1 transition-transform duration-200" style="color: #D4941E;">
                                    View projects
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>

                        <!-- Turnkey Projects -->
                        <div class="bg-white rounded-2xl shadow-xl px-8 py-12 sm:px-12 lg:px-8 hover:shadow-2xl transition-all duration-300 group">
                            <div class="mb-8 space-y-4">
                                <div class="w-16 h-16 rounded-full gradient-primary flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-2xl font-semibold" style="color: #8B5A2B;">Turnkey Projects</h3>
                                <p class="mb-6 text-gray-600 leading-relaxed">Complete design-to-delivery solutions including construction, interiors, and project management.</p>
                                <a href="#portfolio" class="inline-flex items-center font-medium group-hover:translate-x-1 transition-transform duration-200" style="color: #D4941E;">
                                    View projects
                                    <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section id="portfolio">
            <!-- Portfolio Section -->
            <div class="relative py-12 overflow-hidden bg-white sm:py-16">
                <div class="relative px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl">
                    <!-- Section Header -->
                    <div class="max-w-4xl mx-auto text-center mb-16">
                        <p class="text-sm font-normal tracking-widest uppercase mb-4">
                            <span class="text-transparent bg-clip-text text-gradient-brand">Our Work</span>
                        </p>
                        <h2 class="text-3xl font-normal sm:text-4xl lg:text-5xl c-primary mb-6">
                            Featured Projects
                        </h2>
                        <p class="text-lg c-secondary">
                            Discover how we transform spaces through innovative design and expert craftsmanship across our comprehensive range of services.
                        </p>
                    </div>


                    <!-- Portfolio Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                        <!-- Project 1: Design & Consultancy -->
                        <div class="group relative overflow-hidden rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                            <div class="aspect-square overflow-hidden">
                                <img src="NIRMANAH PROJECTS/revised view1.jpg"
                                     alt="Modern architectural design showcasing Nirmanah's design consultancy expertise"
                                     class="w-full h-full object-cover object-center group-hover:scale-110 transition-transform duration-500" />
                            </div>
                            <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
                                    <h3 class="text-xl font-semibold mb-2" style="color: #D4941E;">Modern Residential Design</h3>
                                    <p class="text-sm opacity-90">Comprehensive architectural and interior design solution balancing aesthetics with functionality.</p>
                                    <span class="inline-block mt-3 px-3 py-1 text-xs rounded-full" style="background-color: rgba(212, 148, 30, 0.9);">Design & Consultancy</span>
                                </div>
                            </div>
                        </div>


                        <!-- Project 2: Renovation & Remodeling -->
                        <div class="group relative overflow-hidden rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                            <div class="aspect-square overflow-hidden">
                                <img src="NIRMANAH PROJECTS/PHOTO-2023-05-16-21-47-54.jpg"
                                     alt="Home renovation project showcasing enhanced style and functionality"
                                     class="w-full h-full object-cover object-center group-hover:scale-110 transition-transform duration-500" />
                            </div>
                            <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
                                    <h3 class="text-xl font-semibold mb-2" style="color: #D4941E;">Contemporary Home Renovation</h3>
                                    <p class="text-sm opacity-90">Complete home transformation enhancing style, comfort, and usability for modern living.</p>
                                    <span class="inline-block mt-3 px-3 py-1 text-xs rounded-full" style="background-color: rgba(212, 148, 30, 0.9);">Renovation & Remodeling</span>
                                </div>
                            </div>
                        </div>


                        <!-- Project 3: Landscaping & Outdoor Works -->
                        <div class="group relative overflow-hidden rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                            <div class="aspect-square overflow-hidden">
                                <img src="NIRMANAH PROJECTS/PHOTO-2023-06-06-13-52-28.jpg"
                                     alt="Custom outdoor space with landscaping and architectural elements"
                                     class="w-full h-full object-cover object-center group-hover:scale-110 transition-transform duration-500" />
                            </div>
                            <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
                                    <h3 class="text-xl font-semibold mb-2" style="color: #D4941E;">Outdoor Living Space</h3>
                                    <p class="text-sm opacity-90">Custom outdoor design featuring landscaping elements that blend beauty with function.</p>
                                    <span class="inline-block mt-3 px-3 py-1 text-xs rounded-full" style="background-color: rgba(212, 148, 30, 0.9);">Landscaping & Outdoor Works</span>
                                </div>
                            </div>
                        </div>


                        <!-- Project 4: Turnkey Project -->
                        <div class="group relative overflow-hidden rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                            <div class="aspect-square overflow-hidden">
                                <img src="NIRMANAH PROJECTS/PHOTO-2025-04-08-11-46-53.jpg"
                                     alt="Complete turnkey project from design to delivery"
                                     class="w-full h-full object-cover object-center group-hover:scale-110 transition-transform duration-500" />
                            </div>
                            <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
                                    <h3 class="text-xl font-semibold mb-2" style="color: #D4941E;">Complete Project Solution</h3>
                                    <p class="text-sm opacity-90">End-to-end design-to-delivery solution including construction and project management.</p>
                                    <span class="inline-block mt-3 px-3 py-1 text-xs rounded-full" style="background-color: rgba(212, 148, 30, 0.9);">Turnkey Projects</span>
                                </div>
                            </div>
                        </div>


                        <!-- Project 5: Interior Design -->
                        <div class="group relative overflow-hidden rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                            <div class="aspect-square overflow-hidden">
                                <img src="NIRMANAH PROJECTS/PHOTO-2023-05-16-22-02-55.jpg"
                                     alt="Interior design project showcasing modern aesthetics and functionality"
                                     class="w-full h-full object-cover object-center group-hover:scale-110 transition-transform duration-500" />
                            </div>
                            <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
                                    <h3 class="text-xl font-semibold mb-2" style="color: #D4941E;">Modern Interior Design</h3>
                                    <p class="text-sm opacity-90">Sophisticated interior design combining contemporary aesthetics with practical functionality.</p>
                                    <span class="inline-block mt-3 px-3 py-1 text-xs rounded-full" style="background-color: rgba(212, 148, 30, 0.9);">Design & Consultancy</span>
                                </div>
                            </div>
                        </div>


                        <!-- Project 6: Architectural Design -->
                        <div class="group relative overflow-hidden rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105">
                            <div class="aspect-square overflow-hidden">
                                <img src="NIRMANAH PROJECTS/v1 (1).jpg"
                                     alt="Architectural design project showcasing structural and aesthetic excellence"
                                     class="w-full h-full object-cover object-center group-hover:scale-110 transition-transform duration-500" />
                            </div>
                            <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
                                    <h3 class="text-xl font-semibold mb-2" style="color: #D4941E;">Architectural Excellence</h3>
                                    <p class="text-sm opacity-90">Innovative architectural design showcasing structural integrity and aesthetic appeal.</p>
                                    <span class="inline-block mt-3 px-3 py-1 text-xs rounded-full" style="background-color: rgba(212, 148, 30, 0.9);">Design & Consultancy</span>
                                </div>
                            </div>
                        </div>
                        <!-- From Uiverse.io by andrew-demchenk0 --> 
                        <div class="card">
                            <div class="card__img"><svg viewBox="0 0 128 128" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="st1" height="78.6" transform="matrix(0.9761 0.2175 -0.2175 0.9761 15.4391 -12.3278)" width="101.2" x="13.1" y="24.7"></rect><rect class="st53" height="78.6" transform="matrix(0.9761 0.2175 -0.2175 0.9761 15.4391 -12.3278)" width="101.2" x="13.1" y="24.7"></rect><polygon class="st16" points="116,62 116,35.4 38.1,18 22.1,18 5.8,91.3 76.2,107 106,107"></polygon><polygon class="st1" points="97.2,23 10,23 10,102 111,102 111,36.8"></polygon><polygon class="st53" points="97.2,23 10,23 10,102 111,102 111,36.8"></polygon><g><rect class="st7" height="58" width="80" x="20" y="34"></rect><g><polygon class="st9" points="100.2,92 73.1,44.2 51.2,75.5 40,58.7 20.2,92 39.7,92"></polygon><circle class="st18" cx="57" cy="52" r="11"></circle><polygon class="st1" points="40,58.5 31.6,72.6 34.6,78.2 37.9,75.2 43.5,79.9 47,78.2 51,75.2"></polygon><path class="st1" d="M57.7,66c0,0,4.1,7.2,4.3,6.6c0.2-0.6,6.1-5.6,6.1-5.6l6.9,3.6l1.5-10.3L88.9,72L73.1,44.1L57.7,66z"></path><polygon class="st15" points="73.1,44.2 83.6,92 100.2,92"></polygon><polyline class="st2" points="100.2,91.9 73.1,44.1 39.7,91.9"></polyline><polyline class="st2" points="51.2,75.4 40,58.5 20.2,91.9"></polyline><polygon class="st15" points="51.2,75.4 40,58.5 47,81.3"></polygon><polyline class="st2" points="51.5,91.9 67.1,70.5 80.4,91.9"></polyline><polygon class="st15" points="72.3,92 67.1,70.7 80.4,92"></polygon></g><rect class="st53" height="58" width="80" x="20" y="34"></rect></g><polygon class="st18" points="111,37 97,37 97,23"></polygon><polygon class="st53" points="111,37 97,37 97,23"></polygon></svg></div>
                            <div class="card__subtitle">Type of work</div>
                            <div class="card__wrapper">
                                <div class="card__title">Project name</div>
                                <div class="card__icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" style="user-select: none; width: 100%; height: 100%; display: inline-block; fill: rgb(224, 223, 220); flex-shrink: 0; cursor: auto;" color="rgb(224, 223, 220)"><g color="rgb(224, 223, 220)"><circle cx="128" cy="128" r="96" opacity="0.2"></circle><circle cx="128" cy="128" r="96" fill="none" stroke="rgb(224, 223, 220)" stroke-miterlimit="10" stroke-width="16"></circle><polyline points="134.1 161.9 168 128 134.1 94.1" fill="none" stroke="rgb(224, 223, 220)" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"></polyline><line x1="88" y1="128" x2="168" y2="128" fill="none" stroke="rgb(224, 223, 220)" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"></line></g></svg></div>

                            </div>
                        </div>
                        <!-- From Uiverse.io by andrew-demchenk0 --> 
                        <div class="card">
                            <div class="card__img"><svg viewBox="0 0 128 128" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="st1" height="78.6" transform="matrix(0.9761 0.2175 -0.2175 0.9761 15.4391 -12.3278)" width="101.2" x="13.1" y="24.7"></rect><rect class="st53" height="78.6" transform="matrix(0.9761 0.2175 -0.2175 0.9761 15.4391 -12.3278)" width="101.2" x="13.1" y="24.7"></rect><polygon class="st16" points="116,62 116,35.4 38.1,18 22.1,18 5.8,91.3 76.2,107 106,107"></polygon><polygon class="st1" points="97.2,23 10,23 10,102 111,102 111,36.8"></polygon><polygon class="st53" points="97.2,23 10,23 10,102 111,102 111,36.8"></polygon><g><rect class="st7" height="58" width="80" x="20" y="34"></rect><g><polygon class="st9" points="100.2,92 73.1,44.2 51.2,75.5 40,58.7 20.2,92 39.7,92"></polygon><circle class="st18" cx="57" cy="52" r="11"></circle><polygon class="st1" points="40,58.5 31.6,72.6 34.6,78.2 37.9,75.2 43.5,79.9 47,78.2 51,75.2"></polygon><path class="st1" d="M57.7,66c0,0,4.1,7.2,4.3,6.6c0.2-0.6,6.1-5.6,6.1-5.6l6.9,3.6l1.5-10.3L88.9,72L73.1,44.1L57.7,66z"></path><polygon class="st15" points="73.1,44.2 83.6,92 100.2,92"></polygon><polyline class="st2" points="100.2,91.9 73.1,44.1 39.7,91.9"></polyline><polyline class="st2" points="51.2,75.4 40,58.5 20.2,91.9"></polyline><polygon class="st15" points="51.2,75.4 40,58.5 47,81.3"></polygon><polyline class="st2" points="51.5,91.9 67.1,70.5 80.4,91.9"></polyline><polygon class="st15" points="72.3,92 67.1,70.7 80.4,92"></polygon></g><rect class="st53" height="58" width="80" x="20" y="34"></rect></g><polygon class="st18" points="111,37 97,37 97,23"></polygon><polygon class="st53" points="111,37 97,37 97,23"></polygon></svg></div>
                            <div class="card__subtitle">Type of work</div>
                            <div class="card__wrapper">
                                <div class="card__title">Project name</div>
                                <div class="card__icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" style="user-select: none; width: 100%; height: 100%; display: inline-block; fill: rgb(224, 223, 220); flex-shrink: 0; cursor: auto;" color="rgb(224, 223, 220)"><g color="rgb(224, 223, 220)"><circle cx="128" cy="128" r="96" opacity="0.2"></circle><circle cx="128" cy="128" r="96" fill="none" stroke="rgb(224, 223, 220)" stroke-miterlimit="10" stroke-width="16"></circle><polyline points="134.1 161.9 168 128 134.1 94.1" fill="none" stroke="rgb(224, 223, 220)" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"></polyline><line x1="88" y1="128" x2="168" y2="128" fill="none" stroke="rgb(224, 223, 220)" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"></line></g></svg></div>

                            </div>
                        </div>
                        <!-- From Uiverse.io by andrew-demchenk0 --> 
                        <div class="card">
                            <div class="card__img"><svg viewBox="0 0 128 128" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><rect class="st1" height="78.6" transform="matrix(0.9761 0.2175 -0.2175 0.9761 15.4391 -12.3278)" width="101.2" x="13.1" y="24.7"></rect><rect class="st53" height="78.6" transform="matrix(0.9761 0.2175 -0.2175 0.9761 15.4391 -12.3278)" width="101.2" x="13.1" y="24.7"></rect><polygon class="st16" points="116,62 116,35.4 38.1,18 22.1,18 5.8,91.3 76.2,107 106,107"></polygon><polygon class="st1" points="97.2,23 10,23 10,102 111,102 111,36.8"></polygon><polygon class="st53" points="97.2,23 10,23 10,102 111,102 111,36.8"></polygon><g><rect class="st7" height="58" width="80" x="20" y="34"></rect><g><polygon class="st9" points="100.2,92 73.1,44.2 51.2,75.5 40,58.7 20.2,92 39.7,92"></polygon><circle class="st18" cx="57" cy="52" r="11"></circle><polygon class="st1" points="40,58.5 31.6,72.6 34.6,78.2 37.9,75.2 43.5,79.9 47,78.2 51,75.2"></polygon><path class="st1" d="M57.7,66c0,0,4.1,7.2,4.3,6.6c0.2-0.6,6.1-5.6,6.1-5.6l6.9,3.6l1.5-10.3L88.9,72L73.1,44.1L57.7,66z"></path><polygon class="st15" points="73.1,44.2 83.6,92 100.2,92"></polygon><polyline class="st2" points="100.2,91.9 73.1,44.1 39.7,91.9"></polyline><polyline class="st2" points="51.2,75.4 40,58.5 20.2,91.9"></polyline><polygon class="st15" points="51.2,75.4 40,58.5 47,81.3"></polygon><polyline class="st2" points="51.5,91.9 67.1,70.5 80.4,91.9"></polyline><polygon class="st15" points="72.3,92 67.1,70.7 80.4,92"></polygon></g><rect class="st53" height="58" width="80" x="20" y="34"></rect></g><polygon class="st18" points="111,37 97,37 97,23"></polygon><polygon class="st53" points="111,37 97,37 97,23"></polygon></svg></div>
                            <div class="card__subtitle">Type of work</div>
                            <div class="card__wrapper">
                                <div class="card__title">Project name</div>
                                <div class="card__icon"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 256" style="user-select: none; width: 100%; height: 100%; display: inline-block; fill: rgb(224, 223, 220); flex-shrink: 0; cursor: auto;" color="rgb(224, 223, 220)"><g color="rgb(224, 223, 220)"><circle cx="128" cy="128" r="96" opacity="0.2"></circle><circle cx="128" cy="128" r="96" fill="none" stroke="rgb(224, 223, 220)" stroke-miterlimit="10" stroke-width="16"></circle><polyline points="134.1 161.9 168 128 134.1 94.1" fill="none" stroke="rgb(224, 223, 220)" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"></polyline><line x1="88" y1="128" x2="168" y2="128" fill="none" stroke="rgb(224, 223, 220)" stroke-linecap="round" stroke-linejoin="round" stroke-width="16"></line></g></svg></div>

                            </div>
                        </div>
                    </div>


                    <!-- Call to Action -->
                    <div class="text-center">
                        <div class="relative inline-flex items-center justify-center group">
                            <div class="absolute transition-all duration-200 rounded-full -inset-px gradient-primary shadow-brand"></div>
                            <a href="portfolio.html" title="View Complete Portfolio"
                               class="relative inline-flex items-center justify-center px-8 py-4 text-base font-normal border border-transparent rounded-full c-inverse gradient-primary hover:scale-105 transition-all duration-300"
                               role="button">
                                View Complete Portfolio
                                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    <!-- Structured Data: Organization (update URL and social profiles as applicable) -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": "Nirmanah",
        "url": "https://www.nirmanah.com/",
        "logo": "https://www.nirmanah.com/NIRMANAH LOGO-01.svg"
    }
    </script>

    <script src="main.js" defer></script>
</body>
</html>
